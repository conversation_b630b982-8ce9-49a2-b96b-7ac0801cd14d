import { Actor } from 'apify';
import axios from 'axios';
import dotenv from 'dotenv';

dotenv.config();

const EMAIL_API_URL = process.env.EMAIL_API_URL || 'https://automations-api.8siuvc.easypanel.host/webhook/7b1ef5a4-3b33-4f6b-8a8a-d3b59ac2f64e';
const MAX_EMAILS_PER_MINUTE = 1000;
const BATCH_SIZE = 1000;

const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Global rate limiter state
let emailsSentThisMinute = 0;
let queue = [];
let rateLimitTimerStarted = false;

// Helper to reset the rate limiter every minute
function startRateLimitTimer() {
    if (!rateLimitTimerStarted) {
        rateLimitTimerStarted = true;
        setInterval(() => {
            emailsSentThisMinute = 0;
            processQueue();
        }, 60000);
    }
}

// Process queued batches if under the rate limit
function processQueue() {
    while (queue.length > 0 && emailsSentThisMinute + queue[0].emails.length <= MAX_EMAILS_PER_MINUTE) {
        const { emails, resolve, reject } = queue.shift();
        sendBatch(emails).then(resolve).catch(reject);
    }
}

// Send a batch, respecting the global rate limit
async function sendBatch(emails) {
    // If sending this batch would exceed the rate limit, queue it
    if (emailsSentThisMinute + emails.length > MAX_EMAILS_PER_MINUTE) {
        return new Promise((resolve, reject) => {
            queue.push({ emails, resolve, reject });
        });
    }
    emailsSentThisMinute += emails.length;
    try {
        const response = await axios.post(
            EMAIL_API_URL,
            { emails },
            { headers: { 'Content-Type': 'application/json' } }
        );
        return response.data;
    } catch (error) {
        console.error('Error verifying email batch:', error.message);
        return { error: error.message, emails };
    }
}

const run = async () => {
    await Actor.init();
    startRateLimitTimer();
    const input = await Actor.getInput();
    const emails = input?.emails || [];
    if (!Array.isArray(emails) || emails.length === 0) {
        throw new Error('Input must include a non-empty "emails" array.');
    }

    // Split emails into batches (up to BATCH_SIZE per batch)
    const batches = [];
    for (let i = 0; i < emails.length; i += BATCH_SIZE) {
        batches.push(emails.slice(i, i + BATCH_SIZE));
    }

    // Process all batches concurrently, but rate limiter will queue as needed
    const results = await Promise.all(batches.map(batch => sendBatch(batch)));

    await Actor.setValue('OUTPUT', results);
    console.log('✅ All batches processed (with global rate limiting). Results saved to OUTPUT.');
    await Actor.exit();
};

run();


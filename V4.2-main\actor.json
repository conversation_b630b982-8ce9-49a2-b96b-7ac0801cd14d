{"actorSpecification": 1, "name": "verify-emails-actor", "title": "<PERSON>eri<PERSON> Emails Actor", "description": "Verifies emails using a batch API with rate limiting (max 1000 emails per minute).", "version": "1.0.0", "meta": {"templateId": "node-js"}, "input": {"schemaVersion": 1, "title": "Verify Emails Input", "description": "Input an array of emails to verify.", "type": "object", "required": ["emails"], "properties": {"emails": {"type": "array", "title": "Emails", "description": "Array of email addresses to verify.", "items": {"type": "string"}, "editor": "textarea", "example": ["<EMAIL>", "<EMAIL>"]}}}}
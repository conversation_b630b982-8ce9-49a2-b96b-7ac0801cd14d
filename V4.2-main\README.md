# Verify Emails Actor

This Apify actor verifies emails using a batch API, sending up to 1000 emails per minute to comply with rate limits.

## Input
- `emails`: Array of email addresses to verify (required)

Example input (in Apify):
```json
{
  "emails": [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ]
}
```

## Output
- The actor outputs the verification results for all emails in the Apify key-value store under the key `OUTPUT`.

## Environment Variables
- `EMAIL_API_URL` (optional): Override the default email verification API endpoint.

## Usage
Deploy this actor on Apify, provide your emails as input, and retrieve the results from the OUTPUT key.

